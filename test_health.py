#!/usr/bin/env python3
"""
Simple health check test for Mai Voice Agent
Tests the health endpoints to ensure they work before deployment
"""

import asyncio
import sys
import os
import logging

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

async def test_health_endpoints():
    """Test health endpoints"""
    try:
        # Import the FastAPI app
        from main import app
        import uvicorn
        import httpx
        import threading
        import time

        print("🧪 Starting test server...")

        # Start server in a separate thread
        def run_server():
            uvicorn.run(app, host="127.0.0.1", port=8001, log_level="error")

        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()

        # Wait for server to start
        time.sleep(3)

        print("🧪 Testing health endpoints...")

        async with httpx.AsyncClient() as client:
            # Test root endpoint (returns HTML, so just check status)
            print("Testing GET /")
            response = await client.get("http://127.0.0.1:8001/")
            print(f"Status: {response.status_code}")
            print(f"Content-Type: {response.headers.get('content-type', 'unknown')}")
            assert response.status_code == 200

            # Test health endpoint
            print("\nTesting GET /health")
            response = await client.get("http://127.0.0.1:8001/health")
            print(f"Status: {response.status_code}")
            print(f"Response: {response.json()}")
            assert response.status_code == 200

            # Test API health endpoint
            print("\nTesting GET /api/health")
            response = await client.get("http://127.0.0.1:8001/api/health")
            print(f"Status: {response.status_code}")
            print(f"Response: {response.json()}")
            assert response.status_code == 200

            print("\n✅ All health endpoints working!")
            return True

    except Exception as e:
        print(f"\n❌ Health check failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Set up basic logging
    logging.basicConfig(level=logging.INFO)
    
    # Run the test
    success = asyncio.run(test_health_endpoints())
    
    if success:
        print("\n🎉 Health check test passed! Ready for deployment.")
        sys.exit(0)
    else:
        print("\n💥 Health check test failed! Fix issues before deployment.")
        sys.exit(1)
