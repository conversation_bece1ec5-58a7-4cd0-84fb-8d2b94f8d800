# 🎤 Mai Voice Agent

<div align="center">

![<PERSON>](frontend/Assets/mai.gif)

**A sophisticated real-time voice and video AI assistant powered by Google's Gemini 2.0 Flash**

*Experience natural conversations with <PERSON> through voice, video, and text interactions*

## 🌐 **LIVE DEPLOYMENT**

**🚀 Deployed URL:** https://web-production-72873.up.railway.app

### Quick Access Links:
- **💬 Main Application:** https://web-production-72873.up.railway.app
- **❤️ Health Check:** https://web-production-72873.up.railway.app/api/health
- **🧪 Connection Test:** https://web-production-72873.up.railway.app/test.html

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-Latest-green.svg)](https://fastapi.tiangolo.com)
[![WebRTC](https://img.shields.io/badge/WebRTC-Enabled-orange.svg)](https://webrtc.org)
[![Gemini](https://img.shields.io/badge/Gemini-2.0%20Flash-purple.svg)](https://ai.google.dev)

</div>

---

**Mai** is an intelligent, voice-to-voice AI customer service assistant for **Critical Future** — a world-leading AI & Strategy Development Agency. Mai is designed to be indistinguishable from a top-tier human assistant: emotionally aware, naturally spoken, and incredibly helpful.

## ✨ Features

### 🎙️ **Real-time Voice Chat**
- Natural conversation flow with ultra-low latency
- Advanced voice synthesis with multiple voice options (Aoede, Charon, Fenrir, Kore, Puck)
- Real-time audio processing and response generation
- Visual wave animations during conversations

### 📹 **Interactive Video Chat**
- Mai can see you through your camera
- Responds to visual cues and non-verbal communication
- Real-time video processing with Gemini Vision
- Seamless audio-video synchronization

### 💬 **Intelligent Text Chat**
- Traditional text-based conversations
- Rich message formatting and history
- Instant responses with typing indicators
- Conversation context preservation

### 🎨 **Modern User Experience**
- Sleek dark theme interface
- Responsive design for all devices
- Smooth animations and transitions
- Intuitive controls and navigation

### 📧 **Email Integration**
- Send conversation summaries
- Share important insights
- Automated email formatting
- Secure email delivery

---

## 🏗️ Architecture

### Technology Stack

| Component | Technology | Purpose |
|-----------|------------|---------|
| **Backend** | FastAPI + Python | High-performance async API server |
| **Real-time Communication** | WebRTC + FastRTC | Ultra-low latency audio/video streaming |
| **AI Engine** | Google Gemini 2.0 Flash | Advanced language and vision processing |
| **Frontend** | HTML5 + CSS3 + JavaScript | Modern responsive web interface |
| **Voice Synthesis** | Google's Neural Voices | Natural-sounding speech generation |

### Project Structure
Mai Voice Agent/
├── 🔧 backend/
│ ├── main.py # 🚀 FastAPI application entry point
│ ├── routes.py # 🛣️ API routes and endpoints
│ ├── webrtc_handler.py # 📡 WebRTC and voice/video handling
│ ├── ai_handlers.py # 🤖 Gemini AI integration
│ ├── config.py # ⚙️ Configuration management
│ ├── models.py # 📋 Pydantic data models
│ ├── email_service.py # 📧 Email functionality
│ └── requirements.txt # 📦 Python dependencies
├── 🎨 frontend/
│ ├── index.html # 🏠 Main application interface
│ ├── script.js # ⚡ Frontend JavaScript logic
│ ├── styles.css # 🎭 Styling and animations
│ └── Assets/
│ ├── mai.gif # 👋 Mai's animated avatar
│ └── cf_logo.png # 🏢 Critical Future logo
└── 📖 README.md # 📚 This documentation


---

## 🎯 Usage Guide

### 🎤 Voice Chat
1. Click the **🎙️ Voice Chat** tab
2. Press **Start Voice Chat** button
3. Allow microphone permissions
4. Start speaking naturally to Mai
5. Watch the wave animations as Mai responds

### 📹 Video Chat
1. Click the **📹 Video Chat** tab
2. Press **Start Video Chat** button
3. Allow camera and microphone permissions
4. Mai can now see and hear you
5. Engage in natural conversation with visual context

### 💬 Text Chat
1. Click the **💬 Text Chat** tab
2. Type your message in the input field
3. Press Enter or click Send
4. Enjoy instant text-based conversations

### 📧 Email Features
1. Use the email integration to share conversations
2. Send summaries and insights
3. Automated formatting for professional communication

---

## 🧠 Mai's Capabilities

### Core Responsibilities

1. **Greet callers** with warmth and professionalism
2. **Engage naturally** to understand their reason for calling
3. **Capture and confirm** key details:
   - Full name
   - Company name (if relevant)
   - Email address
   - Phone number (optional)
   - Purpose of enquiry or interest
4. **Ask relevant follow-up questions** to clarify their needs
5. **Repeat back and confirm** the captured details
6. **Generate a structured summary** of the conversation
7. **Trigger follow-up emails** to both caller and Critical Future team

### Email Follow-ups

**To the Caller:**
- Confirmation that Critical Future received their message
- Summary of their enquiry
- Note that a team member will be in touch soon

**To Critical Future Team:**
- Caller's contact details
- Conversation summary in bullet points
- Optional full call transcript

---

## 🔧 Voice Options

- **Aoede** – Warm and friendly (default)
- **Charon** – Deep and authoritative
- **Fenrir** – Energetic and dynamic
- **Kore** – Calm and soothing
- **Puck** – Playful and cheerful

---

## 🛠️ API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/` | GET | Main interface |
| `/chat` | POST | Text chat with Mai |
| `/ws` | WebSocket | Real-time text chat |
| `/submit_contact` | POST | Submit contact form |
| `/input_hook` | POST | Initialize voice chat |
| `/end_session` | POST | End active session |
| `/health` | GET | Health check |
| `/debug` | GET | Debug information |
| `/api/voices` | GET | Available voices |

---

## 🔍 Troubleshooting

### Common Issues

1. **Voice chat not working**
   - Check browser microphone permissions
   - Use text chat as fallback

2. **Email not sending**
   - Ensure proper configuration of credentials
   - Check email service status

3. **Video chat issues**
   - Ensure camera is connected and permissions are granted
   - Use supported browsers (Chrome, Firefox, Edge)

---

## 📞 Support

For technical support or questions:
- **Email**: <EMAIL>
- **Company**: Critical Future
- **Website**: https://criticalfuture.com

---

## 📄 License

This project is proprietary software of Critical Future. All rights reserved.

![Critical Future Logo](frontend/Assets/cf_logo.png)

---

<div align="center">

**Experience the future of AI conversation with Mai Voice Agent**

*Real-time • Intelligent • Interactive*

</div>
